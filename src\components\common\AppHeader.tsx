'use client';
import React, { useState, useEffect } from 'react';
import Image from 'next/image';

const AppHeader: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Search query:', searchQuery);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  // Scroll detection effect
  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      setIsScrolled(scrollY > 0);
    };

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Check initial scroll position
    handleScroll();

    // Cleanup event listener on component unmount
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <header className="w-full bg-gradient-to-r from-[#5a4a7a] to-[#6b5b8b] text-white sticky top-0 z-50 shadow-lg backdrop-blur-sm transition-all duration-300">
      {/* Top Bar */}
      <div className={`w-full bg-gradient-to-r from-[#4a3a6a] to-[#5b4b7b] border-b border-white/10 transition-all duration-300 overflow-hidden ${
        isScrolled
          ? 'max-h-0 py-0 opacity-0'
          : 'max-h-20 py-3 opacity-100'
      }`}>
        <div className="max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-2 text-sm">
            {/* Contact Info */}
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-3 group">
                <span className="text-white/80 group-hover:text-white transition-colors duration-300">📞 CONTACT</span>
                <span className="text-white font-semibold tracking-wide">0966.000.643</span>
              </div>
              <div className="text-white/80 hover:text-white transition-colors duration-300 cursor-default">
                08:00 - 17:00
              </div>
            </div>

            {/* Right Side */}
            <div className="flex items-center gap-4">
              <button className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95 border border-red-400/30">
                CLICK VÀO ĐỂ HỌC
              </button>
              <div className="flex items-center gap-3 text-sm">
                <button className="text-white/90 hover:text-yellow-400 transition-all duration-300 hover:scale-105 font-medium">
                  Đăng nhập
                </button>
                <span className="text-white/50">|</span>
                <button className="text-white/90 hover:text-yellow-400 transition-all duration-300 hover:scale-105 font-medium">
                  Đăng ký
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className={`w-full bg-gradient-to-r from-[#5a4a7a] to-[#6b5b8b] transition-all duration-300 ${
        isScrolled ? 'py-3' : 'py-5'
      }`}>
        <div className="max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between gap-4">
            {/* Logo */}
            <div className="flex items-center gap-3 group">
              <div className={`bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-105 border-2 border-yellow-300/30 ${
                isScrolled ? 'w-10 h-10' : 'w-12 h-12'
              }`}>
                <span className={`text-purple-800 font-bold tracking-tight transition-all duration-300 ${
                  isScrolled ? 'text-lg' : 'text-xl'
                }`}>OM'E</span>
              </div>
            </div>

            {/* Navigation Menu */}
            <nav className="hidden lg:flex items-center gap-8">
              <a href="#" className="relative text-white hover:text-yellow-400 transition-all duration-300 font-semibold py-2 px-3 rounded-lg hover:bg-white/10 group">
                <span className="relative z-10">Trang chủ</span>
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 to-yellow-500/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </a>
              <div className="relative group">
                <button className="relative text-white hover:text-yellow-400 transition-all duration-300 font-semibold flex items-center gap-2 py-2 px-3 rounded-lg hover:bg-white/10">
                  <span className="relative z-10">Giới thiệu</span>
                  <svg className="w-4 h-4 transition-transform duration-300 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                  <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 to-yellow-500/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>
              </div>
              <div className="relative group">
                <button className="relative text-white hover:text-yellow-400 transition-all duration-300 font-semibold flex items-center gap-2 py-2 px-3 rounded-lg hover:bg-white/10">
                  <span className="relative z-10">Khóa học</span>
                  <svg className="w-4 h-4 transition-transform duration-300 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                  <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 to-yellow-500/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>
              </div>
              <a href="#" className="relative text-white hover:text-yellow-400 transition-all duration-300 font-semibold py-2 px-3 rounded-lg hover:bg-white/10 group">
                <span className="relative z-10">Tin tức</span>
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 to-yellow-500/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </a>
              <a href="#" className="relative text-white hover:text-yellow-400 transition-all duration-300 font-semibold py-2 px-3 rounded-lg hover:bg-white/10 group">
                <span className="relative z-10">Liên hệ</span>
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 to-yellow-500/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </a>
              <a href="#" className="relative text-white hover:text-yellow-400 transition-all duration-300 font-semibold py-2 px-3 rounded-lg hover:bg-white/10 group">
                <span className="relative z-10">Sự kiện</span>
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 to-yellow-500/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </a>
            </nav>

            {/* Search and Cart */}
            <div className="flex items-center gap-4">
              {/* Search */}
              <form onSubmit={handleSearch} className="hidden md:flex items-center">
                <div className="relative group">
                  <input
                    type="text"
                    placeholder="Tìm kiếm khóa học..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="bg-white/15 border border-white/30 rounded-xl px-5 py-3 pr-12 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 focus:bg-white/20 w-72 transition-all duration-300 backdrop-blur-sm shadow-inner"
                  />
                  <button
                    type="submit"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/70 hover:text-yellow-400 transition-all duration-300 hover:scale-110 p-1 rounded-lg hover:bg-white/10"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </button>
                </div>
              </form>

              {/* Cart */}
              <button className="relative bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 px-4 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-xl active:scale-95 border border-red-400/30 group">
                <span className="relative z-10 flex items-center gap-2">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0h7M17 18a2 2 0 11-4 0 2 2 0 014 0zM9 18a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </span>
                <span className="absolute -top-2 -right-2 bg-gradient-to-r from-yellow-400 to-yellow-500 text-purple-800 text-xs font-bold rounded-full w-7 h-7 flex items-center justify-center shadow-lg border-2 border-white/30 group-hover:scale-110 transition-transform duration-300">
                  0
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-red-400/30 to-red-500/30 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </button>

              {/* Mobile Menu Button */}
              <button
                onClick={toggleMobileMenu}
                className="lg:hidden text-white hover:text-yellow-400 transition-all duration-300 p-2 rounded-lg hover:bg-white/10 hover:scale-110"
                aria-label="Toggle mobile menu"
              >
                <svg
                  className={`w-6 h-6 transition-transform duration-300 ${isMobileMenuOpen ? 'rotate-90' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  {isMobileMenuOpen ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  )}
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <div className={`lg:hidden bg-gradient-to-r from-[#5a4a7a] to-[#6b5b8b] border-t border-white/10 transition-all duration-300 overflow-hidden ${
        isMobileMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
      }`}>
        <div className="max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="py-4 space-y-2">
            <a
              href="#"
              className="block text-white hover:text-yellow-400 hover:bg-white/10 transition-all duration-300 font-semibold py-3 px-4 rounded-lg group"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              <span className="flex items-center gap-3">
                <span className="w-2 h-2 bg-yellow-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                Trang chủ
              </span>
            </a>

            <button
              className="w-full text-left text-white hover:text-yellow-400 hover:bg-white/10 transition-all duration-300 font-semibold py-3 px-4 rounded-lg group"
            >
              <span className="flex items-center justify-between">
                <span className="flex items-center gap-3">
                  <span className="w-2 h-2 bg-yellow-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                  Giới thiệu
                </span>
                <svg className="w-4 h-4 transition-transform duration-300 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </span>
            </button>

            <button
              className="w-full text-left text-white hover:text-yellow-400 hover:bg-white/10 transition-all duration-300 font-semibold py-3 px-4 rounded-lg group"
            >
              <span className="flex items-center justify-between">
                <span className="flex items-center gap-3">
                  <span className="w-2 h-2 bg-yellow-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                  Khóa học
                </span>
                <svg className="w-4 h-4 transition-transform duration-300 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </span>
            </button>

            <a
              href="#"
              className="block text-white hover:text-yellow-400 hover:bg-white/10 transition-all duration-300 font-semibold py-3 px-4 rounded-lg group"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              <span className="flex items-center gap-3">
                <span className="w-2 h-2 bg-yellow-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                Tin tức
              </span>
            </a>

            <a
              href="#"
              className="block text-white hover:text-yellow-400 hover:bg-white/10 transition-all duration-300 font-semibold py-3 px-4 rounded-lg group"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              <span className="flex items-center gap-3">
                <span className="w-2 h-2 bg-yellow-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                Liên hệ
              </span>
            </a>

            <a
              href="#"
              className="block text-white hover:text-yellow-400 hover:bg-white/10 transition-all duration-300 font-semibold py-3 px-4 rounded-lg group"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              <span className="flex items-center gap-3">
                <span className="w-2 h-2 bg-yellow-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                Sự kiện
              </span>
            </a>

            {/* Mobile Search */}
            <div className="pt-4 border-t border-white/10 mt-4">
              <form onSubmit={handleSearch} className="flex items-center">
                <div className="relative w-full">
                  <input
                    type="text"
                    placeholder="Tìm kiếm khóa học..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full bg-white/15 border border-white/30 rounded-xl px-5 py-3 pr-12 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 focus:bg-white/20 transition-all duration-300 backdrop-blur-sm shadow-inner"
                  />
                  <button
                    type="submit"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/70 hover:text-yellow-400 transition-all duration-300 hover:scale-110 p-1 rounded-lg hover:bg-white/10"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </button>
                </div>
              </form>
            </div>
          </nav>
        </div>
      </div>

      {/* Mobile Menu Backdrop */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-40 transition-opacity duration-300"
          onClick={() => setIsMobileMenuOpen(false)}
          style={{ top: isScrolled ? '76px' : '120px' }} // Adjust based on header height
        />
      )}
    </header>
  );
};

export default AppHeader;
