'use client';
import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import Button from '@/components/ui/Button';
import { getHealthSolutionsByCategory, isApiSuccess } from '@/services/dataService';
import { HealthSolution } from '@/types/data';

const HealthSolutions: React.FC = () => {
  const [topSolutions, setTopSolutions] = useState<HealthSolution[]>([]);
  const [bottomSolutions, setBottomSolutions] = useState<HealthSolution[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Data fetching effect
  useEffect(() => {
    const fetchHealthSolutions = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Fetch top solutions
        const topResponse = await getHealthSolutionsByCategory('top');
        if (isApiSuccess(topResponse)) {
          setTopSolutions(topResponse.data);
        } else {
          throw new Error(topResponse.error);
        }

        // Fetch bottom solutions
        const bottomResponse = await getHealthSolutionsByCategory('bottom');
        if (isApiSuccess(bottomResponse)) {
          setBottomSolutions(bottomResponse.data);
        } else {
          throw new Error(bottomResponse.error);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load health solutions');
        console.error('Error fetching health solutions:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchHealthSolutions();
  }, []);



  // Loading state
  if (isLoading) {
    return (
      <section className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 md:py-16 lg:py-20">
        <div className="bg-global-12 rounded-[50px] p-6 sm:p-8 md:p-12 lg:p-[50px] shadow-lg">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="flex flex-col items-center gap-4">
              <div className="w-12 h-12 border-4 border-global-3 border-t-transparent rounded-full animate-spin"></div>
              <p className="text-global-8 text-lg">Đang tải giải pháp sức khỏe...</p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  // Error state
  if (error) {
    return (
      <section className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 md:py-16 lg:py-20">
        <div className="bg-global-12 rounded-[50px] p-6 sm:p-8 md:p-12 lg:p-[50px] shadow-lg">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="flex flex-col items-center gap-4 text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-global-3 mb-2">Không thể tải dữ liệu</h3>
                <p className="text-global-8">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="mt-4 px-4 py-2 bg-global-3 text-global-11 rounded-lg hover:bg-global-4 transition-colors"
                >
                  Thử lại
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section
      className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 md:py-16 lg:py-20"
    >
      <div
        className="bg-global-12 rounded-[50px] p-6 sm:p-8 md:p-12 lg:p-[50px] shadow-lg hover:shadow-xl transition-all duration-700 ease-out motion-reduce:transition-none health-solutions-enter"
        data-aos="fade-up"
        data-aos-duration="800"
        data-aos-delay="100"
      >
        {/* Main Content Row */}
        <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12">
          {/* Left Content */}
          <div
            className="w-full lg:w-[68%] flex flex-col gap-6 lg:gap-8 health-solutions-content-left"
            data-aos="fade-right"
            data-aos-duration="800"
            data-aos-delay="200"
          >
            <div className="flex flex-col gap-3 lg:gap-4">
              <h1
                className="text-[24px] sm:text-[30px] md:text-[35px] lg:text-[40px] font-bold leading-tight text-global-3 health-solutions-title"
                data-aos="fade-up"
                data-aos-duration="800"
                data-aos-delay="300"
              >
                Giải Pháp Cho Sức Khỏe Bạn
              </h1>
              <p
                className="text-sm sm:text-base text-global-8 leading-relaxed health-solutions-description"
                data-aos="fade-up"
                data-aos-duration="800"
                data-aos-delay="400"
              >
                Chúng tôi hân hạnh cung cấp tới toàn thể quý cộng đồng những dòng giải pháp
                hữu ích giúp mọi người có thể chủ động nâng cao sức khỏe về cả thể chất lẫn
                tinh thần của mình
              </p>
            </div>
            <div
              className="health-solutions-button"
              data-aos="fade-up"
              data-aos-duration="800"
              data-aos-delay="500"
            >
              <Button
                variant="primary"
                size="md"
                className={`
                  health-solutions-button-enhanced rounded-[14px] px-6 py-3 w-fit
                  font-semibold text-base
                  ${isLoading ? 'health-solutions-button-loading' : ''}
                `}
                rightIcon={!isLoading ? "/images/img_arrow_right.svg" : undefined}
                disabled={isLoading}
              >
                {isLoading ? 'ĐANG TẢI...' : 'XEM TẤT CẢ'}
              </Button>
            </div>
          </div>

          {/* Right Content - Top Solutions */}
          <div
            className="w-full lg:w-[32%] flex flex-col gap-4 lg:gap-6 health-solutions-content-right"
            data-aos="fade-left"
            data-aos-duration="800"
            data-aos-delay="300"
          >
            <div className="flex gap-4">
              {topSolutions.map((solution, index) => (
                <div
                  key={solution.id}
                  className={`w-1/2 group cursor-pointer health-solutions-item-${index + 1}`}
                  data-aos="zoom-in"
                  data-aos-duration="600"
                  data-aos-delay={400 + (index * 100)}
                  tabIndex={0}
                  role="button"
                  aria-label={`Xem thêm về ${solution.title}`}
                >
                  <div className="health-solution-card relative overflow-hidden rounded-[14px]">
                    <Image
                      src={solution.image}
                      alt={solution.alt}
                      width={292}
                      height={192}
                      className="health-solution-image w-full h-auto object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent
                      opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-out
                      motion-reduce:opacity-0" />
                  </div>
                </div>
              ))}
            </div>
            <div className="flex justify-between text-center gap-2">
              {topSolutions.map((solution, index) => (
                <h3
                  key={`title-${solution.id}`}
                  className={`health-solution-title flex-1 text-lg sm:text-xl md:text-2xl font-semibold text-global-3 cursor-pointer health-solutions-item-${index + 3}`}
                  data-aos="fade-up"
                  data-aos-duration="600"
                  data-aos-delay={600 + (index * 100)}
                  tabIndex={0}
                  role="button"
                  aria-label={`Tìm hiểu về ${solution.title}`}
                >
                  {solution.title}
                </h3>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Solutions Grid */}
        <div
          className="mt-8 lg:mt-12 health-solutions-grid"
          data-aos="fade-up"
          data-aos-duration="800"
          data-aos-delay="600"
        >
          <div className="flex flex-wrap gap-4 justify-center">
            {bottomSolutions.map((solution, index) => (
              <div
                key={solution.id}
                className={`w-full sm:w-[48%] md:w-[23%] group cursor-pointer health-solutions-item-${index + 3}`}
                data-aos="zoom-in"
                data-aos-duration="600"
                data-aos-delay={700 + (index * 100)}
                tabIndex={0}
                role="button"
                aria-label={`Xem thêm về ${solution.title}`}
              >
                <div className="health-solution-card relative overflow-hidden rounded-[14px]">
                  <Image
                    src={solution.image}
                    alt={solution.alt}
                    width={296}
                    height={192}
                    className="health-solution-image w-full h-auto object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent
                    opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-out
                    motion-reduce:opacity-0" />
                </div>
              </div>
            ))}
          </div>

          {/* Bottom Solutions Titles */}
          <div className="flex flex-wrap justify-between mt-4 lg:mt-6 text-center gap-2">
            {bottomSolutions.map((solution, index) => (
              <h3
                key={`title-${solution.id}`}
                className={`health-solution-title w-full sm:w-[48%] md:w-[23%] text-lg sm:text-xl md:text-2xl font-semibold text-global-3 cursor-pointer health-solutions-item-${index + 7}`}
                data-aos="fade-up"
                data-aos-duration="600"
                data-aos-delay={1100 + (index * 100)}
                tabIndex={0}
                role="button"
                aria-label={`Tìm hiểu về ${solution.title}`}
              >
                {solution.title}
              </h3>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default HealthSolutions;
