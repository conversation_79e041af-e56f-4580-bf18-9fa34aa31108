import React from 'react';
import Image from 'next/image';
import Slider from '@/components/common/Slider';

const HeroSlider: React.FC = () => {
  return (
    <section className="w-full">
      <Slider autoPlay autoPlayInterval={5000}>
        <div className="relative w-full h-full">
          <Image
            src="/images/img_image_750x1920.png"
            alt="Hero slide 1"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-[url('/images/img_image_38.png')] bg-cover bg-center" />
          <div className="absolute top-1/2 right-4 sm:right-8 md:right-16 lg:right-[80px] transform -translate-y-1/2 flex flex-col gap-4">
            <div className="w-[80px] h-[80px] bg-slider-1 rounded-[38px]" />
            <div className="relative w-[86px] h-[86px]">
              <div className="w-full h-full bg-[#2196f3] rounded-[34px]" />
              <Image
                src="/images/img_image_30x32.png"
                alt="Icon"
                width={32}
                height={30}
                className="absolute bottom-4 left-[18px]"
              />
            </div>
            <div className="w-[80px] h-[80px] bg-slider-2 rounded-[38px]" />
            <div className="relative w-[86px] h-[86px]">
              <div className="w-full h-full bg-[#f44336] rounded-[34px]" />
              <Image
                src="/images/img_image_39.png"
                alt="Icon"
                width={32}
                height={32}
                className="absolute bottom-4 left-[18px]"
              />
            </div>
          </div>
        </div>
        <div className="relative w-full h-full">
          <Image
            src="/images/img_image_37.png"
            alt="Hero slide 2"
            fill
            className="object-cover"
          />
        </div>
      </Slider>
    </section>
  );
};

export default HeroSlider;
