import React from 'react';
import Image from 'next/image';

const Footer: React.FC = () => {
  return (
    <div className="w-full bg-[#d9d9d9]">
      <div className="w-full max-w-[1440px] mx-auto px-[28px] sm:px-[42px] lg:px-[56px] py-[36px] sm:py-[54px] lg:py-[72px]">
        <div className="flex flex-col lg:flex-row justify-center items-start w-full gap-8 lg:gap-0">
          {/* Main Content Container */}
          <div className="flex flex-col justify-start items-start w-full lg:w-[64%] gap-6 lg:gap-0">
            {/* Header Row */}
            <div className="flex flex-col lg:flex-row justify-between items-start w-full gap-6 lg:gap-0">
              {/* Company Title */}
              <div className="w-full lg:w-[28%]">
                <h1 className="text-[16px] sm:text-[18px] lg:text-[20px] font-semibold leading-[24px] sm:leading-[26px] lg:leading-[28px] text-left text-white font-inter">
                  Trung Tâm Đào Tạo & Chăm Sóc Sức Khỏe OM'E Việt Nam
                </h1>
              </div>
              {/* Policy Section and About Us */}
              <div className="flex flex-col lg:flex-row justify-start items-center w-full lg:w-[66%] gap-6 lg:gap-0">
                {/* Policy Section */}
                <div className="flex flex-col justify-start items-center w-full lg:flex-1 gap-4">
                  {/* Policy Header Row */}
                  <div className="flex flex-col sm:flex-row justify-start lg:justify-between items-start lg:items-center w-full gap-4 lg:gap-0">
                    <h2 className="text-[16px] sm:text-[18px] lg:text-[20px] font-semibold leading-[20px] sm:leading-[22px] lg:leading-[25px] text-left text-white font-inter w-auto">
                      Chính Sách Và Quy Định Chung
                    </h2>
                    <h3 className="text-[14px] sm:text-[16px] lg:text-[18px] font-bold leading-[18px] sm:leading-[20px] lg:leading-[22px] text-left text-white font-inter w-auto lg:ml-[62px]">
                      Về chúng tôi
                    </h3>
                  </div>
                  {/* Policy Links Row */}
                  <div className="flex flex-col sm:flex-row justify-between items-start w-full gap-4 lg:gap-0">
                    <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter w-auto mb-2">
                      Điều khoản sử dụng
                    </p>
                    <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter w-auto lg:mr-[161px]">
                      Trang chủ
                    </p>
                  </div>
                </div>
              </div>
            </div>
            {/* Contact Information and Policy Links */}
            <div className="flex flex-col justify-start items-start w-full gap-6 lg:gap-0 lg:-mt-[2px]">
              {/* First Row - Address and Privacy Policy */}
              <div className="flex flex-col lg:flex-row justify-start items-start lg:items-center w-full gap-4 lg:gap-0">
                {/* Address */}
                <div className="flex flex-row justify-start items-start w-auto gap-2">
                  <Image
                    src="/images/img_frame_white_a700.svg"
                    alt="Location icon"
                    width={24}
                    height={24}
                    className="w-[24px] h-[24px] mt-[6px]"
                  />
                  <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter">
                    116 Trần Vỹ, Mai Dịch, Cầu Giấy, Hà Nội
                  </p>
                </div>
                {/* Privacy Policy */}
                <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter w-auto lg:ml-auto">
                  Chính sách bảo mật
                </p>
              </div>
              {/* Second Row - Phone and Purchase Policy */}
              <div className="flex flex-col lg:flex-row justify-start items-start lg:items-center w-full gap-4 lg:gap-0">
                {/* Phone */}
                <div className="flex flex-row justify-start items-center w-auto gap-2 mb-1">
                  <Image
                    src="/images/img_frame_white_a700_24x24.svg"
                    alt="Phone icon"
                    width={24}
                    height={24}
                    className="w-[24px] h-[24px]"
                  />
                  <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter">
                    0966.000.643
                  </p>
                </div>
                {/* Purchase Policy */}
                <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter w-auto lg:ml-auto">
                  Chính sách mua hàng và thanh toán
                </p>
              </div>
              {/* Third Row - Email and Refund Policy */}
              <div className="flex flex-col lg:flex-row justify-start items-start lg:items-center w-full gap-4 lg:gap-0">
                {/* Email */}
                <div className="flex flex-row justify-start items-center w-auto gap-2">
                  <Image
                    src="/images/img_frame_24x24.svg"
                    alt="Email icon"
                    width={24}
                    height={24}
                    className="w-[24px] h-[24px]"
                  />
                  <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter">
                    <EMAIL>
                  </p>
                </div>
                {/* Refund Policy */}
                <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter w-auto lg:ml-auto">
                  Chính sách hoàn tiền
                </p>
              </div>
            </div>
            {/* About Us Links Column */}
            <div className="flex flex-col lg:flex-row justify-start items-start lg:items-end w-full gap-4 lg:gap-0">
              {/* Website and Complaint Policy */}
              <div className="flex flex-col lg:flex-row justify-start items-start lg:items-end w-full gap-4 lg:gap-0">
                {/* Website */}
                <div className="flex flex-row justify-start items-center w-auto gap-2 mt-2">
                  <Image
                    src="/images/img_frame_1.svg"
                    alt="Website icon"
                    width={24}
                    height={24}
                    className="w-[24px] h-[24px]"
                  />
                  <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter">
                    https://ome.edu.vn/
                  </p>
                </div>
                {/* Complaint Policy */}
                <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter w-auto">
                  Chính sách khiếu nại
                </p>
                {/* Contact Link */}
                <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter w-auto lg:ml-[135px]">
                  Liên hệ
                </p>
              </div>
              {/* About Us Navigation */}
              <div className="flex flex-col justify-start items-start w-auto gap-4 lg:ml-auto">
                <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter">
                  Giới thiệu
                </p>
                <p className="text-[14px] sm:text-[15px] lg:text-[16px] font-normal leading-[18px] sm:leading-[19px] lg:leading-[20px] text-left text-white font-inter">
                  Tin tức
                </p>
              </div>
            </div>
            {/* Certification Badge */}
            <div className="mt-[14px]">
              <Image
                src="/images/img_image_60x160.png"
                alt="Certification badge"
                width={160}
                height={60}
                className="w-[80px] sm:w-[120px] lg:w-[160px] h-[30px] sm:h-[45px] lg:h-[60px]"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Footer;
