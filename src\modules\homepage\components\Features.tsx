import React from 'react';
import Image from 'next/image';

const Features: React.FC = () => {
  const features = [
    {
      icon: "/images/img_image_32x32.png",
      title: "Phổ Cập Kiến Thức",
      description: "Trao gửi kiến thức hữu ích với tính thực tiễn nhằm nâng cao nhận thức mỗi người về tầm quan trọng của chủ động chăm sóc sức khỏe",
      gradient: "from-blue-500 to-purple-600",
      bgColor: "bg-blue-50",
      delay: 300
    },
    {
      icon: "/images/img_image_5.png",
      title: "Hỗ Trợ Học Tập 24/7",
      description: "Tới với OM'E trong suốt vòng đời học tập của mình chỉ cần có thắc mắc học viên sẽ được bộ phận chăm sóc riêng hỗ trợ 24/7, đảm bảo hiệu quả khóa học ở mức tối đa",
      gradient: "from-green-500 to-teal-600",
      bgColor: "bg-green-50",
      delay: 400
    },
    {
      icon: "/images/img_image_6.png",
      title: "Chuyên Viên Hàng Đầu",
      description: "Liên kết với các chuyên gia hàng đầu, OM'E tự hào với hệ thống giảng viên có chuyên môn cao trong các lĩnh vực, sẵn sàng có thể tư vấn cho học viên mọi vấn đề về sức khỏe",
      gradient: "from-orange-500 to-red-600",
      bgColor: "bg-orange-50",
      delay: 500
    }
  ];

  return (
    <section className="w-full bg-gradient-to-br from-global-12 via-global-6/30 to-global-12 py-12 sm:py-16 md:py-20 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 w-32 h-32 bg-gradient-to-br from-global-3 to-global-4 rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-gradient-to-br from-global-2 to-global-3 rounded-full blur-3xl"></div>
      </div>

      <div className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="flex flex-col lg:flex-row items-center gap-12 lg:gap-16">
          {/* Image Section */}
          <div
            className="w-full lg:w-2/5 relative"
            data-aos="fade-right"
            data-aos-duration="800"
            data-aos-delay="100"
          >
            <div className="relative group max-w-md mx-auto">
              {/* Decorative background */}
              <div className="absolute -inset-3 bg-gradient-to-r from-global-3/20 to-global-4/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-500"></div>

              {/* Main image container */}
              <div className="relative bg-global-12 rounded-2xl shadow-2xl overflow-hidden border border-global-6/50">
                <Image
                  src="/images/img_image_500x500.png"
                  alt="OM'E Features"
                  width={400}
                  height={400}
                  className="w-full h-auto object-cover group-hover:scale-105 transition-transform duration-700"
                />

                {/* Overlay gradient */}
                <div className="absolute inset-0 bg-gradient-to-t from-global-3/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>

              {/* Floating accent */}
              <div className="absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full opacity-20 animate-float"></div>
            </div>
          </div>

          {/* Features Section */}
          <div
            className="w-full lg:w-3/5 flex flex-col gap-8"
            data-aos="fade-left"
            data-aos-duration="800"
            data-aos-delay="200"
          >
            {/* Section Header */}
            <div className="text-center lg:text-left mb-4">
              <div className="inline-flex items-center gap-2 bg-gradient-to-r from-global-3 to-global-4 text-global-11 px-4 py-2 rounded-full text-sm font-medium mb-4">
                <span className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></span>
                Tính Năng Nổi Bật
              </div>
              <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-global-3 leading-tight">
                Tại Sao Chọn <span className="bg-clip-text bg-gradient-to-r from-global-3 to-global-4">OM'E</span>?
              </h2>
            </div>

            {/* Features List */}
            <div className="space-y-6">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className="group relative cursor-default"
                  data-aos="fade-up"
                  data-aos-duration="600"
                  data-aos-delay={feature.delay}
                >
                  {/* Feature Card */}
                  <div className="relative bg-global-12 rounded-xl p-4 shadow-lg hover:shadow-2xl transition-all duration-500 border border-global-6/30 hover:border-global-3/30 group-hover:transform group-hover:-translate-y-1">
                    {/* Background gradient on hover */}
                    <div className={`absolute inset-0 bg-gradient-to-r ${feature.gradient} opacity-0 group-hover:opacity-5 rounded-xl transition-opacity duration-500`}></div>

                    <div className="relative z-10 flex gap-3">
                      {/* Icon Container */}
                      <div className={`flex-shrink-0 w-12 h-12 ${feature.bgColor} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-md`}>
                        <div className={`w-8 h-8 bg-gradient-to-br ${feature.gradient} rounded-md flex items-center justify-center shadow-inner`}>
                          <Image
                            src={feature.icon}
                            alt="Feature Icon"
                            width={18}
                            height={18}
                            className="filter brightness-0 invert"
                          />
                        </div>
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg sm:text-xl font-bold text-global-3 mb-2 group-hover:text-global-4 transition-colors duration-300">
                          {feature.title}
                        </h3>
                        <p className="text-sm text-global-5 leading-relaxed group-hover:text-global-4 transition-colors duration-300">
                          {feature.description}
                        </p>
                      </div>
                    </div>

                    {/* Hover accent line */}
                    <div className={`absolute bottom-0 left-4 right-4 h-1 bg-gradient-to-r ${feature.gradient} rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left`}></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;
