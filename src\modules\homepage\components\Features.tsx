import React from 'react';
import Image from 'next/image';

const Features: React.FC = () => {
  return (
    <section className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 md:py-16">
      <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12">
        <div
          className="w-full lg:w-1/2"
          data-aos="fade-right"
          data-aos-duration="800"
          data-aos-delay="100"
        >
          <Image
            src="/images/img_image_500x500.png"
            alt="OM'E Features"
            width={500}
            height={500}
            className="w-full h-auto rounded-lg"
          />
        </div>
        <div
          className="w-full lg:w-1/2 flex flex-col gap-8"
          data-aos="fade-left"
          data-aos-duration="800"
          data-aos-delay="200"
        >
          <div
            className="flex flex-col gap-4"
            data-aos="fade-up"
            data-aos-duration="600"
            data-aos-delay="300"
          >
            <Image src="/images/img_image_32x32.png" alt="Icon" width={32} height={32} />
            <h3 className="text-xl sm:text-2xl font-semibold text-global-2">
              Phổ Cập Kiến Thức
            </h3>
            <p className="text-sm sm:text-base text-global-1 leading-relaxed">
              Trao gửi kiến thức hữu ích với tính thực tiễn nhằm nâng cao nhận thức mỗi người
              về tầm quan trọng của chủ động chăm sóc sức khỏe
            </p>
          </div>
          <div
            className="flex flex-col gap-4"
            data-aos="fade-up"
            data-aos-duration="600"
            data-aos-delay="400"
          >
            <Image src="/images/img_image_5.png" alt="Icon" width={32} height={32} />
            <h3 className="text-xl sm:text-2xl font-semibold text-global-2">
              Hỗ Trợ Học Tập 24/7
            </h3>
            <p className="text-sm sm:text-base text-global-1 leading-relaxed">
              Tới với OM'E trong suốt vòng đời học tập của mình chỉ cần có thắc mắc học viên
              sẽ được bộ phận chăm sóc riêng hỗ trợ 24/7, đảm bảo hiệu quả khóa học ở mức tối
              đa
            </p>
          </div>
          <div
            className="flex flex-col gap-4"
            data-aos="fade-up"
            data-aos-duration="600"
            data-aos-delay="500"
          >
            <Image src="/images/img_image_6.png" alt="Icon" width={32} height={32} />
            <h3 className="text-xl sm:text-2xl font-semibold text-global-2">
              Chuyên Viên Hàng Đầu
            </h3>
            <p className="text-sm sm:text-base text-global-1 leading-relaxed">
              Liên kết với các chuyên gia hàng đầu, OM'E tự hào với hệ thống giảng viên có
              chuyên môn cao trong các lĩnh vực, sẵn sàng có thể tư vấn cho học viên mọi vấn
              đề về sức khỏe
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;
