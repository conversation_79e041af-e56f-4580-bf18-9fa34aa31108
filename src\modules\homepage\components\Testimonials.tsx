import React, { useState } from 'react';
import Image from 'next/image';
import { Testimonial } from '@/types/data';

interface TestimonialsProps {
  testimonials: Testimonial[];
}

const Testimonials: React.FC<TestimonialsProps> = ({ testimonials }) => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  if (testimonials.length === 0) {
    return null;
  }

  return (
    <section className="w-full bg-global-8 py-8 sm:py-12 md:py-16">
      <div className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center gap-8">
          <div className="text-center max-w-2xl">
            <Image
              src={testimonials[currentTestimonial].image}
              alt={testimonials[currentTestimonial].name}
              width={80}
              height={80}
              className="w-20 h-20 rounded-full mx-auto mb-4"
            />
            <Image
              src="/images/img_frame_yellow_900.svg"
              alt="Quote"
              width={24}
              height={24}
              className="mx-auto mb-5"
            />
            <p className="text-sm sm:text-base text-global-5 leading-relaxed mb-5">
              {testimonials[currentTestimonial].content}
            </p>
            <h4 className="text-base sm:text-lg font-semibold text-global-4 mb-1">
              {testimonials[currentTestimonial].name}
            </h4>
            <p className="text-sm text-global-8">{testimonials[currentTestimonial].title}</p>
          </div>
          <div className="flex gap-3">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentTestimonial(index)}
                className={`w-2 h-2 rounded-full transition-colors duration-200 ${
                  index === currentTestimonial ? 'bg-global-2' : 'bg-global-1'
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
