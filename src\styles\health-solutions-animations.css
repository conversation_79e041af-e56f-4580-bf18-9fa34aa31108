/* Health Solutions Component Animations */

/* Enhanced <PERSON><PERSON> */
.health-solutions-button-enhanced {
  background: var(--global-text-3) !important;
  color: var(--global-text-11) !important;
  border: 2px solid var(--global-text-3) !important;
  box-shadow:
    0 4px 12px rgba(70, 50, 102, 0.25),
    0 2px 4px rgba(70, 50, 102, 0.15) !important;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  position: relative;
  overflow: hidden;
}

.health-solutions-button-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.health-solutions-button-enhanced:hover {
  background: var(--global-text-4) !important;
  border-color: var(--global-text-4) !important;
  box-shadow:
    0 8px 24px rgba(74, 48, 109, 0.35),
    0 4px 8px rgba(74, 48, 109, 0.25) !important;
  transform: translateY(-2px) scale(1.02) !important;
}

.health-solutions-button-enhanced:hover::before {
  left: 100%;
}

.health-solutions-button-enhanced:active {
  transform: translateY(0) scale(1) !important;
  box-shadow:
    0 2px 8px rgba(70, 50, 102, 0.3),
    0 1px 2px rgba(70, 50, 102, 0.2) !important;
}

.health-solutions-button-enhanced:focus-visible {
  outline: 3px solid var(--global-text-4);
  outline-offset: 2px;
  box-shadow:
    0 8px 24px rgba(74, 48, 109, 0.35),
    0 4px 8px rgba(74, 48, 109, 0.25),
    0 0 0 3px rgba(74, 48, 109, 0.3) !important;
}

/* Enhanced contrast for better accessibility */
.health-solutions-button-enhanced {
  font-weight: 600 !important;
  letter-spacing: 0.025em;
  text-transform: uppercase;
  min-height: 48px;
  min-width: 160px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .health-solutions-button-enhanced {
    border-width: 3px !important;
    font-weight: 700 !important;
  }

  .health-solutions-button-enhanced:hover {
    border-width: 3px !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .health-solutions-button-enhanced {
    box-shadow:
      0 4px 12px rgba(70, 50, 102, 0.4),
      0 2px 4px rgba(70, 50, 102, 0.3) !important;
  }

  .health-solutions-button-enhanced:hover {
    box-shadow:
      0 8px 24px rgba(74, 48, 109, 0.5),
      0 4px 8px rgba(74, 48, 109, 0.4) !important;
  }
}

/* Loading state for button */
.health-solutions-button-loading {
  opacity: 0.7;
  cursor: not-allowed;
  pointer-events: none;
}

.health-solutions-button-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Accessibility: Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .health-solutions-button-enhanced {
    transition: none !important;
  }

  .health-solutions-button-enhanced::before {
    display: none;
  }

  .health-solutions-button-enhanced:hover {
    transform: none !important;
  }
}

/* Custom entrance animations with better performance */
.health-solutions-enter {
  animation: healthSolutionsEnter 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.health-solutions-content-left {
  animation: slideInLeft 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s both;
}

.health-solutions-content-right {
  animation: slideInRight 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s both;
}

.health-solutions-title {
  animation: slideUp 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s both;
}

.health-solutions-description {
  animation: slideUp 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.4s both;
}

.health-solutions-button {
  animation: slideUp 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.5s both;
}

.health-solutions-grid {
  animation: slideUp 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.4s both;
}

/* Staggered animations for grid items */
.health-solutions-item-1 {
  animation: scaleIn 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.6s both;
}

.health-solutions-item-2 {
  animation: scaleIn 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.7s both;
}

.health-solutions-item-3 {
  animation: scaleIn 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.8s both;
}

.health-solutions-item-4 {
  animation: scaleIn 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.9s both;
}

.health-solutions-item-5 {
  animation: scaleIn 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) 1.0s both;
}

.health-solutions-item-6 {
  animation: scaleIn 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) 1.1s both;
}

.health-solutions-item-7 {
  animation: scaleIn 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) 1.2s both;
}

.health-solutions-item-8 {
  animation: scaleIn 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) 1.3s both;
}

.health-solutions-item-9 {
  animation: scaleIn 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) 1.4s both;
}

.health-solutions-item-10 {
  animation: scaleIn 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) 1.5s both;
}

/* Hover effects with smooth transitions */
.health-solution-card {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center;
  will-change: transform, box-shadow;
}

.health-solution-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.health-solution-card:active {
  transform: translateY(-2px) scale(1.01);
}

.health-solution-image {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
}

.health-solution-card:hover .health-solution-image {
  transform: scale(1.05);
}

.health-solution-title {
  transition: color 0.2s ease-out;
  will-change: color;
}

.health-solution-title:hover {
  color: var(--global-text-4);
}

/* Keyframe definitions */
@keyframes healthSolutionsEnter {
  0% {
    opacity: 0;
    transform: translateY(2rem);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-2rem);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(2rem);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(1.5rem);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(1rem);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Focus states for accessibility */
.health-solution-card:focus-visible {
  outline: 2px solid var(--global-text-4);
  outline-offset: 2px;
}

.health-solution-title:focus-visible {
  outline: 2px solid var(--global-text-4);
  outline-offset: 2px;
  border-radius: 4px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .health-solution-card {
    border: 1px solid currentColor;
  }
  
  .health-solution-card:hover {
    border-width: 2px;
  }
}

/* Print styles */
@media print {
  .health-solutions-enter,
  .health-solutions-content-left,
  .health-solutions-content-right,
  .health-solutions-title,
  .health-solutions-description,
  .health-solutions-button,
  .health-solutions-grid,
  .health-solutions-item-1,
  .health-solutions-item-2,
  .health-solutions-item-3,
  .health-solutions-item-4,
  .health-solutions-item-5,
  .health-solutions-item-6,
  .health-solutions-item-7,
  .health-solutions-item-8,
  .health-solutions-item-9,
  .health-solutions-item-10 {
    animation: none !important;
    opacity: 1 !important;
    transform: none !important;
  }
}
