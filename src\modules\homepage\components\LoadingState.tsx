import React from 'react';

interface LoadingStateProps {
  message?: string;
}

const LoadingState: React.FC<LoadingStateProps> = ({ 
  message = "Đang tải dữ liệu..." 
}) => {
  return (
    <div className="w-full min-h-screen bg-global-12 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-global-3 mx-auto mb-4"></div>
        <p className="text-global-3 text-lg">{message}</p>
      </div>
    </div>
  );
};

export default LoadingState;
