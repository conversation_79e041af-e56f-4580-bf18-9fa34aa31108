import React from 'react';
import Image from 'next/image';
import Button from '@/components/ui/Button';
import { Instructor } from '@/types/data';

interface InstructorsProps {
  instructors: Instructor[];
  onInstructorClick: (instructorId: string) => void;
}

const Instructors: React.FC<InstructorsProps> = ({ 
  instructors, 
  onInstructorClick 
}) => {
  return (
    <section className="w-full bg-global-12 py-8 sm:py-12 md:py-16">
      <div className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center gap-8">
          <Button
            variant="primary"
            size="sm"
            className="bg-global-12 text-button-2 shadow-[0px_4px_30px_#eb725744]"
            leftIcon="/images/img_frame.svg"
          >
            GIÁO VIÊN
          </Button>
          <div className="text-center">
            <h2 className="text-[24px] sm:text-[30px] md:text-[36px] font-bold text-global-4 mb-2">
              Đ<PERSON><PERSON> ngũ giáo viên xuất sắc
            </h2>
            <p className="text-sm sm:text-base text-global-5">
              Đội ngũ giáo viên giàu kinh nghiệm, tận tâm và chuyên nghiệp của chúng tôi
            </p>
          </div>
          <div className="w-full overflow-x-auto">
            <div className="flex gap-6 pb-4" style={{ width: 'max-content' }}>
              {instructors.map((instructor) => (
                <div
                  key={instructor.id}
                  className="flex-shrink-0 w-[282px] cursor-pointer hover:scale-105 transition-transform duration-200"
                  onClick={() => onInstructorClick(instructor.id)}
                >
                  <div className="bg-global-12 border border-gray-200 rounded-[24px] shadow-sm overflow-hidden">
                    <div className="relative">
                      <div className="w-16 h-16 bg-gradient-to-r from-[#d4af37cc] to-[#f0d68acc] rounded-[24px] absolute top-0 left-0 z-10" />
                      <div className="relative">
                        <Image
                          src={instructor.image}
                          alt={instructor.name}
                          width={280}
                          height={294}
                          className="w-full h-[294px] object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent to-[#4a306dcc] flex items-end">
                          <span className="text-sm text-global-8 p-4">Xem chi tiết</span>
                        </div>
                      </div>
                    </div>
                    <div className="p-6 text-center">
                      <div className="w-16 h-1 bg-gradient-to-r from-[#d4af37] to-[#f0d68a] mx-auto mb-5" />
                      <h3 className="text-lg sm:text-xl font-semibold text-global-3 mb-1">
                        {instructor.name}
                      </h3>
                      <p className="text-sm text-global-8">{instructor.title}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className="flex gap-3">
            {Array.from({ length: 8 }, (_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === 3 ? 'bg-global-2' : 'bg-global-1'
                }`}
              />
            ))}
          </div>
          <Button
            variant="outline"
            size="md"
            className="border border-global-4 text-global-4 bg-global-12 hover:bg-global-4 hover:text-global-11 rounded-[20px]"
          >
            Xem tất cả giáo viên →
          </Button>
        </div>
      </div>
    </section>
  );
};

export default Instructors;
