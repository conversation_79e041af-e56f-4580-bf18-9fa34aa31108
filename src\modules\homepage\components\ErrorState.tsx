import React from 'react';
import Button from '@/components/ui/Button';

interface ErrorStateProps {
  error: string;
  onRetry: () => void;
}

const ErrorState: React.FC<ErrorStateProps> = ({ error, onRetry }) => {
  return (
    <div className="w-full min-h-screen bg-global-12 flex items-center justify-center">
      <div className="text-center max-w-md mx-auto p-6">
        <div className="text-red-500 text-6xl mb-4">⚠️</div>
        <h2 className="text-global-3 text-xl font-bold mb-2">C<PERSON> lỗi xảy ra</h2>
        <p className="text-global-8 mb-4">{error}</p>
        <Button
          variant="primary"
          size="md"
          className="bg-global-3 text-global-11"
          onClick={onRetry}
        >
          Thử lại
        </Button>
      </div>
    </div>
  );
};

export default ErrorState;
