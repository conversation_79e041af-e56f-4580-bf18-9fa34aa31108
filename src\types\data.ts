export interface Course {
  id: string;
  title: string;
  description: string;
  price: string;
  instructor: string;
  instructorImage: string;
  courseImage: string;
  categoryId: string; // Foreign key reference to Category
  level: 'beginner' | 'intermediate' | 'advanced';
  duration: string;
  rating: number;
  studentsCount: number;
  isPopular: boolean;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

export interface Instructor {
  id: string;
  name: string;
  title: string;
  image: string;
}

export interface Article {
  id: string;
  title: string;
  excerpt: string;
  image: string;
}

export interface Testimonial {
  id: string;
  name: string;
  title: string;
  image: string;
  content: string;
}



// Health Solution embedded in category metadata
export interface HealthSolution {
  id: string;
  title: string;
  image: string;
  alt: string;
  description?: string;
  order: number;
}

// Hierarchical Category System
export interface Category {
  id: string;
  name: string;
  description: string;
  parentId: string | null;
  level: number; // 0 = root, 1 = main category, 2 = subcategory
  order: number;
  isActive: boolean;
  metadata: {
    icon?: string;
    color?: string;
    displayPosition?: 'top' | 'bottom' | 'featured';
    healthSolution?: HealthSolution; // Embedded health solution data
  };
}

export interface CategoriesData {
  categories: Category[];
}



export type Partner = string;

// API Response types to simulate real API behavior
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  timestamp: string;
}

export interface ApiError {
  success: false;
  error: string;
  code: number;
  timestamp: string;
}
