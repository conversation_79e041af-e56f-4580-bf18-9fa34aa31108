<svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1_443)">
<rect x="-8.49858" y="11.3362" width="70.03" height="70.03" rx="34.7523" transform="rotate(-16.4532 -8.49858 11.3362)" fill="#2196F3"/>
</g>
<defs>
<filter id="filter0_d_1_443" x="0" y="0" width="70" height="70" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feColorMatrix type="matrix" values="0 0 0 0 0.129412 0 0 0 0 0.588235 0 0 0 0 0.952941 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_443"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_443" result="shape"/>
</filter>
</defs>
</svg>
