import React from 'react';
import Image from 'next/image';
import Button from '@/components/ui/Button';

const CtaSection: React.FC = () => {
  return (
    <section className="w-full bg-global-12 py-8 sm:py-12 md:py-16">
      <div className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12">
          <div className="w-full lg:w-1/2 flex flex-col gap-6">
            <Button
              variant="primary"
              size="sm"
              className="bg-global-12 text-button-2 shadow-[0px_4px_30px_#eb725744] w-fit"
              leftIcon="/images/img_frame.svg"
            >
              Hợp Tác Cùng Chúng Tôi
            </Button>
            <h2 className="text-[20px] sm:text-[24px] md:text-[31px] font-black text-global-4 leading-tight">
              <PERSON><PERSON><PERSON> hành cùng OM'E trao đi giá trị sức khỏe đích thực cho cộng đồng
            </h2>
            <div className="flex flex-col gap-2">
              <p className="text-sm sm:text-base text-global-8">
                Liên hệ ngay với chúng tôi qua số
              </p>
              <div className="flex items-center gap-2">
                <span className="text-sm sm:text-base text-global-8">hotline</span>
                <span className="text-sm sm:text-base font-bold text-global-10">
                  0966.000.643
                </span>
              </div>
              <p className="text-sm sm:text-base text-global-8">để được cộng tác</p>
              <p className="text-sm sm:text-base text-global-8">
                Chúng tôi trân trọng và rất hân hạnh được đồng hành!
              </p>
            </div>
            <Image
              src="/images/img_image_102x182.png"
              alt="Contact"
              width={182}
              height={102}
              className="w-[182px] h-[102px] rounded-[50px]"
            />
          </div>
          <div className="w-full lg:w-1/2">
            <Image
              src="/images/img_image_500x608.png"
              alt="Partnership"
              width={608}
              height={500}
              className="w-full h-auto rounded-lg"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default CtaSection;
