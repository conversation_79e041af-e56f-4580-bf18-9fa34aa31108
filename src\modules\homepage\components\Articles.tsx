import React from 'react';
import Image from 'next/image';
import Button from '@/components/ui/Button';
import { Article } from '@/types/data';

interface ArticlesProps {
  articles: Article[];
  onArticleClick: (articleId: string) => void;
}

const Articles: React.FC<ArticlesProps> = ({ articles, onArticleClick }) => {
  return (
    <section className="w-full bg-global-12 py-8 sm:py-12 md:py-16">
      <div className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center gap-8">
          <Button
            variant="primary"
            size="sm"
            className="bg-global-12 text-button-2 shadow-[0px_4px_30px_#eb725744]"
            leftIcon="/images/img_frame.svg"
          >
            TIN TỨC
          </Button>
          <h2 className="text-[24px] sm:text-[30px] md:text-[36px] font-bold text-global-4 text-center">
            <PERSON><PERSON><PERSON> bà<PERSON> viết mới nhất
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full">
            {articles.map((article) => (
              <div
                key={article.id}
                className="bg-global-12 rounded-lg shadow-[0px_1px_2px_#0000000c] overflow-hidden cursor-pointer hover:scale-105 transition-transform duration-200"
                onClick={() => onArticleClick(article.id)}
              >
                <Image
                  src={article.image}
                  alt={article.title}
                  width={400}
                  height={256}
                  className="w-full h-[200px] sm:h-[240px] md:h-[256px] object-cover"
                />
                <div className="p-6">
                  <h3 className="text-base sm:text-lg font-semibold text-global-4 mb-2 line-clamp-2">
                    {article.title}
                  </h3>
                  <p className="text-sm text-global-8 line-clamp-3 leading-relaxed">
                    {article.excerpt}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Articles;
