'use client';
import React, { useState, useEffect } from 'react';

interface SliderProps {
  children: React.ReactNode[];
  className?: string;
  showControls?: boolean;
  showIndicators?: boolean;
  autoPlay?: boolean;
  autoPlayInterval?: number;
}

const Slider: React.FC<SliderProps> = ({
  children,
  className = '',
  showControls = true,
  showIndicators = true,
  autoPlay = false,
  autoPlayInterval = 3000
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const goToPrevious = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? children.length - 1 : prevIndex - 1
    );
  };

  const goToNext = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === children.length - 1 ? 0 : prevIndex + 1
    );
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  React.useEffect(() => {
    if (autoPlay) {
      const interval = setInterval(goToNext, autoPlayInterval);
      return () => clearInterval(interval);
    }
  }, [autoPlay, autoPlayInterval, currentIndex]);

  return (
    <div className={`relative w-full overflow-hidden ${className}`}>
      {/* Slider Container */}
      <div className="relative w-full h-[375px] sm:h-[400px] md:h-[500px] lg:h-[750px]">
        <div 
          className="flex transition-transform duration-300 ease-in-out h-full"
          style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        >
          {children.map((child, index) => (
            <div key={index} className="w-full flex-shrink-0 h-full">
              {child}
            </div>
          ))}
        </div>
      </div>

      {/* Navigation Controls */}
      {showControls && (
        <>
          <button
            onClick={goToPrevious}
            className="absolute left-4 sm:left-6 md:left-8 lg:left-[67px] top-1/2 transform -translate-y-1/2 w-[48px] h-[48px] bg-slider-3 rounded-[24px] shadow-[0px_4px_6px_#00000019] flex items-center justify-center hover:bg-opacity-90 transition-all duration-200 z-10"
            aria-label="Previous slide"
          >
            <img src="/images/img_arrow_left.svg" alt="Previous" className="w-6 h-6" />
          </button>
          
          <button
            onClick={goToNext}
            className="absolute right-4 sm:right-6 md:right-8 lg:right-[67px] top-1/2 transform -translate-y-1/2 w-[48px] h-[48px] bg-slider-3 rounded-[24px] shadow-[0px_4px_6px_#00000019] flex items-center justify-center hover:bg-opacity-90 transition-all duration-200 z-10"
            aria-label="Next slide"
          >
            <img src="/images/img_arrow_right_blue_gray_800_02.svg" alt="Next" className="w-6 h-6" />
          </button>
        </>
      )}

      {/* Indicators */}
      {showIndicators && (
        <div className="absolute bottom-4 sm:bottom-6 md:bottom-8 left-1/2 transform -translate-x-1/2 flex gap-[16px] z-10">
          {children.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-[8px] h-[8px] rounded-[4px] transition-all duration-200 ${
                index === currentIndex ? 'bg-global-2' : 'bg-global-1'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default Slider;