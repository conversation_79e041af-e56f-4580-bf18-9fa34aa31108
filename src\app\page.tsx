'use client';
import React, { useEffect } from 'react';
import AOS from 'aos';
import 'aos/dist/aos.css';
import AppHeader from '@/components/common/AppHeader';
import { useHomePageData } from '@/hooks/useHomePageData';
import {
  LoadingState,
  ErrorState,
  HeroSlider,
  HealthSolutions,
  Features,
  HotCourses,
  AboutOme,
  Instructors,
  Articles,
  Testimonials,
  Partners,
  CtaSection,
} from '@/modules/homepage/components';
import AppFooter from '@/components/common/AppFooter';
const HomePage: React.FC = () => {
  // Initialize AOS (Animate On Scroll)
  useEffect(() => {
    AOS.init({
      duration: 800,
      easing: 'ease-out-cubic',
      once: true,
      offset: 100,
      delay: 0,
      anchorPlacement: 'top-bottom',
      disable: 'phone', // Disable on mobile for better performance
    });

    // Refresh AOS when component mounts
    AOS.refresh();

    // Cleanup on unmount
    return () => {
      AOS.refresh();
    };
  }, []);

  // Load data using custom hook
  const { data, isLoading, error, refetch } = useHomePageData();
  const { categories, courses, instructors, articles, testimonials, partners } = data;

  const handleCourseClick = (courseId: string) => {
    console.log('Course clicked:', courseId);
  };
  const handleInstructorClick = (instructorId: string) => {
    console.log('Instructor clicked:', instructorId);
  };
  const handleArticleClick = (articleId: string) => {
    console.log('Article clicked:', articleId);
  };

  // Show loading state
  if (isLoading) {
    return <LoadingState />;
  }

  // Show error state
  if (error) {
    return <ErrorState error={error} onRetry={refetch} />;
  }

  return (
    <>
      <AppHeader />
      <div className="w-full bg-global-12 overflow-hidden">
        <HeroSlider />
        {/* Main Content */}
        <div className="w-full flex flex-col items-center">
          <div data-aos="fade-up" data-aos-duration="800" data-aos-delay="100">
            <HealthSolutions />
          </div>
          <div data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
            <Features />
          </div>
          {/* <div data-aos="fade-up" data-aos-duration="800" data-aos-delay="300"> */}
          <HotCourses categories={categories} courses={courses} onCourseClick={handleCourseClick} />
          {/* </div> */}
          {/* <div data-aos="fade-up" data-aos-duration="800" data-aos-delay="100"> */}
          <AboutOme />
          {/* </div> */}
          {/* <div data-aos="fade-up" data-aos-duration="800" data-aos-delay="200"> */}
          <Instructors instructors={instructors} onInstructorClick={handleInstructorClick} />
          {/* </div> */}
          {/* <div data-aos="fade-up" data-aos-duration="800" data-aos-delay="300"> */}
          <Articles articles={articles} onArticleClick={handleArticleClick} />
          {/* </div> */}
          {/* <div data-aos="fade-up" data-aos-duration="800" data-aos-delay="100"> */}
          <Testimonials testimonials={testimonials} />
          {/* </div> */}
          {/* <div data-aos="fade-up" data-aos-duration="800" data-aos-delay="200"> */}
          <Partners partners={partners} />
          {/* </div> */}
          {/* <div data-aos="fade-up" data-aos-duration="800" data-aos-delay="300"> */}
          <CtaSection />
          {/* </div> */}
        </div>
      </div>

      <AppFooter />
    </>
  );
};
export default HomePage;
