# Data Refactoring Documentation

## Overview
This document describes the refactoring of hardcoded data in `src/app/page.tsx` into a proper service architecture with JSON data files and TypeScript service layer.

## Changes Made

### 1. Data Extraction
Extracted the following hardcoded data arrays from `page.tsx`:
- `categories` - Course categories
- `courses` - Course information
- `instructors` - Instructor profiles
- `articles` - Blog articles/news
- `testimonials` - User testimonials
- `partners` - Partner company logos

### 2. JSON Data Files Created
- `src/data/categories.json` - Course categories
- `src/data/courses.json` - Course data with instructor info
- `src/data/instructors.json` - Instructor profiles
- `src/data/articles.json` - Article/blog content
- `src/data/testimonials.json` - User testimonials
- `src/data/partners.json` - Partner logo paths

### 3. TypeScript Types
Created `src/types/data.ts` with:
- `Course` interface
- `Instructor` interface
- `Article` interface
- `Testimonial` interface
- `Category` type alias
- `Partner` type alias
- `ApiResponse<T>` interface for API responses
- `ApiError` interface for error handling

### 4. Service Layer
Created `src/services/dataService.ts` with:
- Async functions that simulate API calls
- Proper error handling with 5% random error simulation
- Configurable delays to simulate network latency
- Type-safe responses using TypeScript generics
- Utility functions for response validation

#### Service Functions:
- `getCategories()` - Fetch all categories
- `getCourses()` - Fetch all courses
- `getInstructors()` - Fetch all instructors
- `getArticles()` - Fetch all articles
- `getTestimonials()` - Fetch all testimonials
- `getPartners()` - Fetch all partners
- `getCourseById(id)` - Fetch specific course
- `getInstructorById(id)` - Fetch specific instructor
- `getArticleById(id)` - Fetch specific article
- `isApiSuccess(response)` - Type guard for response validation

### 5. Component Updates
Updated `src/app/page.tsx`:
- Replaced hardcoded arrays with React state
- Added `useEffect` hook to load data on component mount
- Implemented loading states with spinner
- Added error handling with retry functionality
- Maintained all existing functionality

### 6. Error Handling Features
- **Loading State**: Shows spinner while data is loading
- **Error State**: Shows error message with retry button
- **Graceful Degradation**: Individual data failures are logged but don't crash the app
- **Simulated API Behavior**: Random errors and delays to test real-world scenarios

### 7. Testing
Created `src/services/__tests__/dataService.test.ts` with:
- Unit tests for all service functions
- Response validation tests
- Error handling tests
- Type safety verification

## Benefits

### 1. Maintainability
- Data is now centralized in JSON files
- Easy to update content without touching component code
- Clear separation of concerns

### 2. Scalability
- Service layer can easily be extended to use real APIs
- Type safety ensures data consistency
- Modular architecture supports future enhancements

### 3. Real-world Simulation
- Async behavior mimics real API calls
- Error handling prepares for production scenarios
- Loading states improve user experience

### 4. Developer Experience
- TypeScript provides excellent IntelliSense
- Clear interfaces make data structure obvious
- Comprehensive error messages aid debugging

## Usage Examples

```typescript
// Loading categories
const categoriesResponse = await getCategories();
if (isApiSuccess(categoriesResponse)) {
  console.log(categoriesResponse.data); // string[]
} else {
  console.error(categoriesResponse.error);
}

// Loading a specific course
const courseResponse = await getCourseById('1');
if (isApiSuccess(courseResponse)) {
  const course = courseResponse.data; // Course | null
  if (course) {
    console.log(course.title);
  }
}
```

## Future Enhancements

1. **Real API Integration**: Replace JSON imports with actual HTTP calls
2. **Caching**: Add response caching to improve performance
3. **Pagination**: Implement pagination for large datasets
4. **Search/Filter**: Add search and filtering capabilities
5. **Optimistic Updates**: Implement optimistic UI updates
6. **Background Sync**: Add background data synchronization

## Migration Notes

- All existing functionality is preserved
- Component behavior remains identical to users
- Data structure is maintained for backward compatibility
- No breaking changes to the public interface

## Testing the Changes

1. Start the development server: `npm run dev`
2. Navigate to the homepage
3. Observe loading state briefly appears
4. Verify all data loads correctly
5. Test error handling by refreshing during load
6. Run tests: `npm test src/services/__tests__/dataService.test.ts`
