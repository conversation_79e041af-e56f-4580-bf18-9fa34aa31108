import React from 'react';
import Image from 'next/image';

interface PartnersProps {
  partners: string[];
}

const Partners: React.FC<PartnersProps> = ({ partners }) => {
  return (
    <section className="w-full bg-global-12 py-8 sm:py-12">
      <div className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h2 className="text-xl sm:text-2xl font-bold text-global-1">
            Th<PERSON><PERSON><PERSON><PERSON>
          </h2>
        </div>
        <div className="flex flex-wrap justify-center items-center gap-8 sm:gap-12">
          {partners.map((partner, index) => (
            <Image
              key={index}
              src={partner}
              alt={`Partner ${index + 1}`}
              width={120}
              height={48}
              className="h-12 w-auto object-contain opacity-70 hover:opacity-100 transition-opacity duration-200"
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Partners;
