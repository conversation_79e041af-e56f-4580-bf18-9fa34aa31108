import React from 'react';
import Image from 'next/image';
import Button from '@/components/ui/Button';

const AboutOme: React.FC = () => {
  return (
    <section className="w-full bg-global-7 py-8 sm:py-12 md:py-16">
      <div className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12">
          <div className="w-full lg:w-1/2 flex flex-col gap-6">
            <h2 className="text-[24px] sm:text-[30px] md:text-[40px] font-bold text-global-3">
              Về OM'E Việt Nam
            </h2>
            <p className="text-sm sm:text-base text-global-5 leading-relaxed">
              Chúng tôi sinh ra bởi sự thôi thúc mang tới sức khỏe và giá trị thực sự từ bên
              trong mỗi người Việt
            </p>
            <div className="flex gap-2">
              <Image src="/images/img_image_24x32.png" alt="Quote" width={32} height={24} />
              <p className="text-sm sm:text-base font-bold text-global-6 leading-relaxed">
                Trong suốt chặng đường phát triển, OM'E thật may mắn được kết hợp với các
                chuyên gia có cùng tầm nhìn, từ đó đóng góp rất nhiều cho sự nâng cao nhận
                thức của mỗi người mỗi nhà trong việc chủ động chăm sóc sức khỏe
              </p>
            </div>
            <p className="text-sm sm:text-base text-global-5 leading-relaxed">
              OM'E rất mong mỗi học viên khi học cùng chúng tôi hãy mang theo tinh thần nghiêm
              túc, sự kiên trì và cam kết thực nghiệm theo nội dung được giảng dạy để đảm bảo
              mỗi khóa học thực hiện được nhiệm vụ trao đi giá trị cho người học của chính nó!
            </p>
            <Button
              variant="primary"
              size="md"
              className="bg-global-3 text-global-11 rounded-[14px] w-fit"
              rightIcon="/images/img_arrow_right.svg"
            >
              ĐỌC THÊM
            </Button>
          </div>
          <div className="w-full lg:w-1/2 relative">
            <Image
              src="/images/img_image_508x960.png"
              alt="OM'E Statistics"
              width={960}
              height={508}
              className="w-full h-auto rounded-lg"
            />
            <div className="absolute top-4 left-4 flex flex-col gap-4">
              <div className="bg-global-9 rounded-[24px] p-4 text-global-11">
                <div className="flex items-end gap-2">
                  <span className="text-2xl sm:text-3xl font-bold">1452</span>
                  <div className="flex flex-col text-xs sm:text-sm">
                    <span>Chuyên gia liên kết cùng OM'E</span>
                    <span>Phương pháp chuẩn quốc tế</span>
                  </div>
                </div>
              </div>
              <div className="bg-global-9 rounded-[24px] p-4 text-global-11">
                <div className="flex items-center gap-3">
                  <span className="text-2xl sm:text-3xl font-bold">+13000</span>
                  <span className="text-xs sm:text-sm">Học viên trên toàn quốc</span>
                </div>
              </div>
              <div className="bg-global-9 rounded-[24px] p-4 text-global-11">
                <div className="flex flex-col gap-1">
                  <div className="flex items-center gap-3">
                    <span className="text-2xl sm:text-3xl font-bold">10</span>
                    <span className="text-xs sm:text-sm">Năm kinh nghiệm</span>
                  </div>
                  <span className="text-xs sm:text-sm">Phương pháp online tiện lợi</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutOme;
